# import zstandard as zstd

# # Read compressed file
# with open("ps_meta.jsonl.zst", "rb") as compressed:
#     dctx = zstd.ZstdDecompressor()
#     with open("pashto_data.txt", "wb") as out:
#         dctx.copy_stream(compressed, out)

# print("File decompressed successfully!")





import os
from transformers import AutoTokenizer

def count_tokens_in_files():
    """
    Count tokens in Pashto text files using XLM-RoBERTa tokenizer
    """
    print("🚀 Starting token counting...")

    try:
        # Initialize tokenizer
        print("📥 Loading XLM-RoBERTa tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained("xlm-roberta-base")
        print("✅ Tokenizer loaded successfully!")

        # List of files to process
        files = ["pashto_cleaned_full_dataset.csv"]
        total_tokens = 0
        processed_files = 0

        print("\n📊 Processing files...")
        print("="*50)

        for filename in files:
            if os.path.exists(filename):
                print(f"📄 Processing: {filename}")

                try:
                    with open(filename, "r", encoding="utf-8") as f:
                        text = f.read()

                    # Count tokens
                    tokens = tokenizer.tokenize(text)
                    file_token_count = len(tokens)
                    total_tokens += file_token_count
                    processed_files += 1

                    # File statistics
                    file_size = len(text)
                    print(f"   📝 Characters: {file_size:,}")
                    print(f"   🔤 Tokens: {file_token_count:,}")
                    print(f"   📈 Tokens/Char ratio: {file_token_count/file_size:.3f}")

                except Exception as e:
                    print(f"   ❌ Error reading {filename}: {e}")
            else:
                print(f"⚠️  File not found: {filename}")

        print("="*50)
        print(f"📊 SUMMARY:")
        print(f"   ✅ Files processed: {processed_files}")
        print(f"   🔤 Total tokens: {total_tokens:,}")

        if total_tokens > 0:
            print(f"   💾 Token size estimate: ~{total_tokens * 4} bytes")
            print(f"   📚 Approximate training data size: {total_tokens:,} tokens")

        return total_tokens

    except ImportError:
        print("❌ Error: transformers library not installed")
        print("💡 Install with: pip install transformers")
        return 0
    except Exception as e:
        print(f"❌ Error: {e}")
        return 0

if __name__ == "__main__":
    count_tokens_in_files()
