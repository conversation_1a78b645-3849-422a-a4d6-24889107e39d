#!/usr/bin/env python3
"""
Simple script to run the Wikipedia data extraction
"""

import sys
import os

def main():
    print("🚀 Starting Pashto Wikipedia Data Extraction...")
    print("="*60)
    
    try:
        # Import and run the extraction
        import wikipidia
        print("✅ Wikipedia extraction completed successfully!")
        
    except ImportError as e:
        print(f"❌ Missing required packages: {e}")
        print("Please install required packages:")
        print("pip install wikipedia-api pandas")
        return 1
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        return 1
    
    print("\n🎉 Data extraction completed!")
    print("Check the following files in your directory:")
    print("  - pashto_wikipedia_lobah.csv")
    print("  - pashto_wikipedia_lobah.txt")
    print("  - pashto_wikipedia_articles.csv")
    print("  - pashto_wikipedia_articles.txt")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
