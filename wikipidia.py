import wikipediaapi
import pandas as pd
import time
import random
from datetime import datetime

def extract_pashto_wikipedia_articles(titles, output_file_base, save_csv=True, save_txt=True):
    """
    Extract multiple articles from Pashto Wikipedia

    Args:
        titles: List of article titles to extract
        output_file_base: Base name for output files (without extension)
        save_csv: Whether to save as CSV file
        save_txt: Whether to save as TXT file
    """
    # Use a proper user agent as required by Wikipedia's policy
    wiki_ps = wikipediaapi.Wikipedia(
        user_agent='PashtoDataCollector/1.0 (<EMAIL>)',
        language='ps'
    )

    data = []
    successful_extractions = 0
    failed_extractions = 0

    print(f"🚀 Starting extraction of {len(titles)} articles...")
    print("="*50)

    for i, title in enumerate(titles, 1):
        try:
            print(f"📖 [{i}/{len(titles)}] Extracting: {title}")
            page = wiki_ps.page(title)

            if page.exists():
                article_data = {
                    "Title": title,
                    "Pashto_Text": page.text,
                    "URL": page.fullurl,
                    "Summary": page.summary[:500] + "..." if len(page.summary) > 500 else page.summary,
                    "Word_Count": len(page.text.split()),
                    "Extraction_Date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                data.append(article_data)
                successful_extractions += 1
                print(f"   ✅ Success - {len(page.text)} characters extracted")
            else:
                print(f"   ❌ Article not found: {title}")
                failed_extractions += 1

            # Add small delay to be respectful to Wikipedia servers
            time.sleep(random.uniform(0.5, 1.5))

        except Exception as e:
            print(f"   ⚠️ Error extracting {title}: {str(e)}")
            failed_extractions += 1
            continue

    # Save results
    if data:
        if save_csv:
            csv_path = f"{output_file_base}.csv"
            df = pd.DataFrame(data)
            df.to_csv(csv_path, index=False, encoding='utf-8')
            print(f"✅ CSV data saved to {csv_path}")

        if save_txt:
            txt_path = f"{output_file_base}.txt"
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(f"Pashto Wikipedia Articles - Extracted on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("="*80 + "\n\n")

                for entry in data:
                    f.write(f"Title: {entry['Title']}\n")
                    f.write(f"URL: {entry['URL']}\n")
                    f.write(f"Word Count: {entry['Word_Count']}\n")
                    f.write(f"Extraction Date: {entry['Extraction_Date']}\n")
                    f.write("-" * 40 + "\n")
                    f.write("SUMMARY:\n")
                    f.write(entry['Summary'] + "\n\n")
                    f.write("FULL TEXT:\n")
                    f.write(entry['Pashto_Text'].strip() + "\n\n")
                    f.write("="*80 + "\n\n")
            print(f"✅ TXT data saved to {txt_path}")

    # Print summary
    print("\n" + "="*50)
    print(f"📊 EXTRACTION SUMMARY:")
    print(f"   ✅ Successful: {successful_extractions}")
    print(f"   ❌ Failed: {failed_extractions}")
    print(f"   📝 Total articles extracted: {len(data)}")
    if data:
        total_words = sum(entry['Word_Count'] for entry in data)
        print(f"   📖 Total words collected: {total_words:,}")
    print("="*50)

    return data

def extract_from_wikipedia_url(url, output_file_base):
    """
    Extract article from a specific Wikipedia URL

    Args:
        url: Full Wikipedia URL
        output_file_base: Base name for output files
    """
    # Extract title from URL
    # URL format: https://ps.wikipedia.org/wiki/TITLE
    if '/wiki/' in url:
        title = url.split('/wiki/')[-1].split('?')[0]  # Remove query parameters
        # URL decode the title
        import urllib.parse
        title = urllib.parse.unquote(title)

        print(f"🔗 Extracting from URL: {url}")
        print(f"📖 Article title: {title}")

        # Use the existing function to extract
        return extract_pashto_wikipedia_articles([title], output_file_base)
    else:
        print(f"❌ Invalid Wikipedia URL format: {url}")
        return []

# ✅ Game and Sports related Pashto Wikipedia articles to extract
game_related_articles = [
    "لوبه",              # Game/Sport (from your link)
    "فوټبال",            # Football/Soccer
    "کرکټ",              # Cricket
    "هاکي",              # Hockey
    "باسکټبال",          # Basketball
    "والیبال",           # Volleyball
    "ټنیس",              # Tennis
    "بکسنګ",             # Boxing
    "کشتي",              # Wrestling
    "شطرنج",             # Chess
    "کبډي",              # Kabaddi
    "بډمنټن",            # Badminton
    "تیراندازي",         # Archery
    "لامبو",             # Swimming
    "دوړه",              # Running/Athletics
    "ورزش",              # Sports (general)
    "اولمپیک لوبې",       # Olympic Games
    "د نړۍ جام",         # World Cup
    "ملي لوبې",          # National Games
    "دودیزې لوبې",       # Traditional Games
]

# Extract all articles
if __name__ == "__main__":
    # First, extract the specific article from your URL
    specific_url = "https://ps.wikipedia.org/wiki/%D9%84%D9%88%D8%A8%D9%87"
    print("🎯 Extracting specific article from your URL...")
    extract_from_wikipedia_url(specific_url, r"D:\Collect data\pashto_wikipedia_lobah")

    print("\n" + "="*60)
    print("🚀 Now extracting multiple articles...")
    print("="*60)

    # Then extract all game-related articles in the list
    output_base = r"D:\Collect data\pashto_wikipedia_games"
    extracted_data = extract_pashto_wikipedia_articles(
        titles=game_related_articles,
        output_file_base=output_base,
        save_csv=True,
        save_txt=True
    )
