from wikipediaapi import Wikipedia
import pandas as pd

def extract_pashto_wikipedia_articles(titles, output_csv):
    # Use a proper user agent as required by Wikipedia's policy
    wiki_ps = Wikipedia(user_agent='PashtoDataCollector/1.0 (<EMAIL>)', language='ps')
    data = []

    for title in titles:
        page = wiki_ps.page(title)
        if page.exists():
            data.append({"Title": title, "Pashto_Text": page.text})
        else:
            print(f"❌ Article not found: {title}")

    # Save as TXT file
    txt_path = output_csv.rsplit('.', 1)[0] + '.txt'
    with open(txt_path, 'w', encoding='utf-8') as f:
        for entry in data:
            f.write(f"Title: {entry['Title']}\n")
            f.write(entry['Pashto_Text'].strip() + "\n\n" + ("="*40) + "\n\n")
    print(f"✅ Wikipedia data saved to {txt_path}")

# ✅ Pashto Wikipedia All Pages: https://ps.wikipedia.org/wiki/ځانګړی:AllPages
# ✅ Titles of articles you want to extract
titles = ["پښتو", "افغانستان", "تاریخ", "کابل", "اسلام"]

# ✅ Where to save the CSV file
extract_pashto_wikipedia_articles(titles, r"D:\Collect data\pashto_wikipedia.csv")
