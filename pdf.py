import pdfplumber
import pandas as pd
from datetime import datetime
import os

# ✅ Enhanced function to extract PDF text in both CSV and TXT formats
def extract_pashto_from_pdf():
    pdf_path = r"D:\Collect data\12_Pashto_Convo.pdf"
    csv_path = r"D:\Collect data\12_Pashto_Convo_output.csv"
    txt_path = r"D:\Collect data\12_Pashto_Convo_output.txt"

    print("🚀 Starting PDF text extraction...")
    print(f"📁 PDF File: {pdf_path}")

    # Check if PDF exists
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return

    data = []
    all_text = []

    try:
        with pdfplumber.open(pdf_path) as pdf:
            print(f"📖 Total pages: {len(pdf.pages)}")

            for i, page in enumerate(pdf.pages, start=1):
                print(f"📄 Extracting page {i}...")
                text = page.extract_text()
                if text:
                    # For CSV format
                    lines = text.split('\n')
                    for line in lines:
                        if line.strip():
                            data.append({"Page": i, "Pashto_Text": line.strip()})

                    # For TXT format
                    all_text.append(f"\n--- PAGE {i} ---\n")
                    all_text.append(text)
                    all_text.append("\n")

        # Save as CSV
        df = pd.DataFrame(data)
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"✅ CSV saved to: {csv_path}")

        # Save as TXT
        with open(txt_path, 'w', encoding='utf-8') as txt_file:
            txt_file.write("PASHTO CONVERSATION - TEXT EXTRACTION\n")
            txt_file.write("="*50 + "\n")
            txt_file.write(f"Extracted on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            txt_file.write(f"Source: {os.path.basename(pdf_path)}\n")
            txt_file.write(f"Total Pages: {len(pdf.pages)}\n")
            txt_file.write("="*50 + "\n")
            txt_file.writelines(all_text)

        print(f"✅ TXT saved to: {txt_path}")
        print(f"📊 Total lines extracted: {len(data)}")

    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        print("💡 Make sure pdfplumber is installed: pip install pdfplumber")

def extract_pashto_grammar_pdf():
    """
    Extract text from Pashto Grammar PDF
    """
    pdf_path = r"D:\Collect data\pashto Grammer.pdf"
    txt_path = r"D:\Collect data\pashto_grammar_extracted.txt"
    csv_path = r"D:\Collect data\pashto_grammar_extracted.csv"

    print("🚀 Starting Pashto Grammar PDF text extraction...")
    print(f"📁 PDF File: {pdf_path}")

    # Check if PDF exists
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return

    data = []
    all_text = []

    try:
        with pdfplumber.open(pdf_path) as pdf:
            print(f"📖 Total pages: {len(pdf.pages)}")

            for i, page in enumerate(pdf.pages, start=1):
                print(f"📄 Extracting page {i}...")
                text = page.extract_text()
                if text:
                    # For CSV format
                    lines = text.split('\n')
                    for line in lines:
                        if line.strip():
                            data.append({"Page": i, "Pashto_Text": line.strip()})

                    # For TXT format
                    all_text.append(f"\n--- PAGE {i} ---\n")
                    all_text.append(text)
                    all_text.append("\n")

        # Save as CSV
        df = pd.DataFrame(data)
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"✅ CSV saved to: {csv_path}")

        # Save as TXT
        with open(txt_path, 'w', encoding='utf-8') as txt_file:
            txt_file.write("PASHTO GRAMMAR - TEXT EXTRACTION\n")
            txt_file.write("="*50 + "\n")
            txt_file.write(f"Extracted on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            txt_file.write(f"Source: {os.path.basename(pdf_path)}\n")
            txt_file.write(f"Total Pages: {len(pdf.pages)}\n")
            txt_file.write("="*50 + "\n")
            txt_file.writelines(all_text)

        print(f"✅ TXT saved to: {txt_path}")
        print(f"📊 Total lines extracted: {len(data)}")

    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        print("💡 Make sure pdfplumber is installed: pip install pdfplumber")

# ✅ Call the functions
if __name__ == "__main__":
    print("Choose extraction option:")
    print("1. Extract 12_Pashto_Convo.pdf")
    print("2. Extract pashto Grammer.pdf")
    print("3. Extract both")

    choice = input("Enter choice (1/2/3): ").strip()

    if choice == "1":
        extract_pashto_from_pdf()
    elif choice == "2":
        extract_pashto_grammar_pdf()
    elif choice == "3":
        extract_pashto_from_pdf()
        print("\n" + "="*60)
        extract_pashto_grammar_pdf()
    else:
        print("Invalid choice. Extracting Pashto Grammar PDF by default...")
        extract_pashto_grammar_pdf()












