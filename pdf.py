import pdfplumber
import pandas as pd

# ✅ Correct function definition
def extract_pashto_from_pdf():
    pdf_path = r"D:\Collect data\12_Pashto_Convo.pdf"
    csv_path = r"D:\Collect data\12_Pashto_Convo_output.csv"
    
    data = []

    with pdfplumber.open(pdf_path) as pdf:
        for i, page in enumerate(pdf.pages, start=1):
            text = page.extract_text()
            if text:
                lines = text.split('\n')
                for line in lines:
                    if line.strip():
                        data.append({"Page": i, "Pashto_Text": line.strip()})

    df = pd.DataFrame(data)
    df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    print(f"✅ Saved to {csv_path}")

# ✅ Call the function
extract_pashto_from_pdf()












