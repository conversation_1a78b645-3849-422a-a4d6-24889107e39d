# import pdfplumber
# import pandas as pd

# # ✅ Correct function definition
# def extract_pashto_from_pdf():
#     pdf_path = r"D:\Collect data\12_Pashto_Convo.pdf"
#     csv_path = r"D:\Collect data\12_Pashto_Convo_output.csv"
    
#     data = []

#     with pdfplumber.open(pdf_path) as pdf:
#         for i, page in enumerate(pdf.pages, start=1):
#             text = page.extract_text()
#             if text:
#                 lines = text.split('\n')
#                 for line in lines:
#                     if line.strip():
#                         data.append({"Page": i, "Pashto_Text": line.strip()})

#     df = pd.DataFrame(data)
#     df.to_csv(csv_path, index=False, encoding='utf-8-sig')
#     print(f"✅ Saved to {csv_path}")

# # ✅ Call the function
# extract_pashto_from_pdf()











import pdfplumber
import os
import pandas as pd

def extract_pashto_from_pdf(pdf_path, csv_path):
    data = []
    with pdfplumber.open(pdf_path) as pdf:
        for i, page in enumerate(pdf.pages, start=1):
            text = page.extract_text()
            if text:
                lines = text.split('\n')
                for line in lines:
                    if line.strip():
                        data.append({"Page": i, "Pashto_Text": line.strip()})

    df = pd.DataFrame(data)
    try:
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        # Save as JSON
        json_path = os.path.splitext(csv_path)[0] + '.json'
        df.to_json(json_path, orient='records', force_ascii=False, indent=2)
        print(f"✅ Saved to {csv_path} and {json_path}")
    except PermissionError as e:
        print(f"❌ Permission denied: {e}\nPlease close the output file(s) if they are open and try again.")

# ✅ Example usage:
pdf_path = r"D:\Collect data\pashto Grammer.pdf"
csv_path = r"D:\Collect data\pashto_Grammer_output.csv"

extract_pashto_from_pdf(pdf_path, csv_path)

