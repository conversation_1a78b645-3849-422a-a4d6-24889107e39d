import requests
from bs4 import BeautifulSoup
import pandas as pd
import time

# Step 1: Main category URL
category_url = "https://ethw.org/Category:Computing_and_electronics"

# Step 2: Get all article links from category page
response = requests.get(category_url)
soup = BeautifulSoup(response.content, 'html.parser')

article_links = []

# Extract article URLs listed in the category
for li in soup.select('div.mw-category li a'):
    link = li.get('href')
    full_url = "https://ethw.org" + link
    article_links.append(full_url)

print(f"✅ Found {len(article_links)} article links")

# Step 3: Function to extract title and content from each article
def extract_article(url):
    try:
        res = requests.get(url)
        soup = BeautifulSoup(res.content, 'html.parser')

        title = soup.find('h1').text.strip()
        content_div = soup.find('div', {'class': 'mw-parser-output'})

        paragraphs = content_div.find_all(['p', 'ul'])  # only body content
        content = "\n".join(p.get_text(strip=True) for p in paragraphs)

        return {
            "title": title,
            "url": url,
            "text": content
        }
    except Exception as e:
        print(f"❌ Error scraping {url}: {e}")
        return None

# Step 4: Scrape all articles and collect data
data = []

for idx, link in enumerate(article_links[:10]):  # Adjust limit as needed
    print(f"🔎 Scraping ({idx+1}): {link}")
    result = extract_article(link)
    if result:
        data.append(result)
    time.sleep(1)

# Step 5: Save to CSV
df = pd.DataFrame(data)
df.to_csv("ethw_computing_articles.csv", index=False, encoding="utf-8-sig")

print("\n✅ Done! Data saved to 'ethw_computing_articles.csv'")
