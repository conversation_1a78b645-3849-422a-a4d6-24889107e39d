# Install required libraries
# pip install requests beautifulsoup4 feedgen pandas

import requests
from bs4 import BeautifulSoup
from feedgen.feed import FeedGenerator
import pandas as pd
import re

# URL of the BBC Pashto topic page
topic_url = "https://www.bbc.com/pashto/topics/cz74k7wy49jt"

# Function to clean text
def clean_text(text):
    text = re.sub(r'\s+', ' ', text).strip()
    return text

# Fetch and parse the page
response = requests.get(topic_url)
soup = BeautifulSoup(response.content, 'html.parser')

# Find article blocks (BBC uses <a> with qa-heading-link class for articles)
articles_html = soup.find_all('a', {'class': 'qa-heading-link'})

articles = []

for article in articles_html:
    title = clean_text(article.get_text())
    link = "https://www.bbc.com" + article['href']
    
    # Optional: get article summary by visiting each link
    try:
        art_resp = requests.get(link)
        art_soup = BeautifulSoup(art_resp.content, 'html.parser')
        paragraphs = art_soup.find_all('p')
        full_text = clean_text(" ".join([p.get_text() for p in paragraphs]))
    except:
        full_text = ""
    
    articles.append({
        'title': title,
        'link': link,
        'summary': full_text
    })

# Save articles to CSV for NLP use
df = pd.DataFrame(articles)
df.to_csv("pashto_articles.csv", index=False, encoding='utf-8-sig')
print(f"Saved {len(articles)} articles to pashto_articles.csv")

# Generate RSS feed
fg = FeedGenerator()
fg.title('Custom BBC Pashto RSS Feed')
fg.link(href=topic_url, rel='alternate')
fg.description('RSS feed generated from BBC Pashto topic page')

for art in articles:
    fe = fg.add_entry()
    fe.title(art['title'])
    fe.link(href=art['link'])
    fe.description(art['summary'])

# Save RSS feed to XML
fg.rss_file('custom_pashto_rss.xml')
print("RSS feed saved as custom_pashto_rss.xml")
