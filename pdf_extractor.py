import PyPDF2
import fitz  # PyMuPDF
import os
from datetime import datetime

def extract_text_from_pdf_pypdf2(pdf_path, output_txt_path):
    """
    Extract text from PDF using PyPDF2 library
    """
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text_content = []
            
            print(f"📖 Total pages in PDF: {len(pdf_reader.pages)}")
            
            for page_num, page in enumerate(pdf_reader.pages, 1):
                print(f"📄 Extracting page {page_num}...")
                page_text = page.extract_text()
                if page_text.strip():
                    text_content.append(f"\n{'='*60}\n")
                    text_content.append(f"PAGE {page_num}\n")
                    text_content.append(f"{'='*60}\n\n")
                    text_content.append(page_text)
                    text_content.append(f"\n\n")
            
            # Save to TXT file
            with open(output_txt_path, 'w', encoding='utf-8') as txt_file:
                txt_file.write(f"Pashto Grammar PDF - Text Extraction\n")
                txt_file.write(f"Extracted on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                txt_file.write(f"Source PDF: {pdf_path}\n")
                txt_file.write(f"Total Pages: {len(pdf_reader.pages)}\n")
                txt_file.write("="*80 + "\n\n")
                txt_file.writelines(text_content)
            
            print(f"✅ Text extracted successfully using PyPDF2!")
            print(f"📝 Output saved to: {output_txt_path}")
            return True
            
    except Exception as e:
        print(f"❌ Error with PyPDF2: {e}")
        return False

def extract_text_from_pdf_pymupdf(pdf_path, output_txt_path):
    """
    Extract text from PDF using PyMuPDF (fitz) library - Better for complex PDFs
    """
    try:
        doc = fitz.open(pdf_path)
        text_content = []
        
        print(f"📖 Total pages in PDF: {doc.page_count}")
        
        for page_num in range(doc.page_count):
            page = doc[page_num]
            print(f"📄 Extracting page {page_num + 1}...")
            page_text = page.get_text()
            
            if page_text.strip():
                text_content.append(f"\n{'='*60}\n")
                text_content.append(f"PAGE {page_num + 1}\n")
                text_content.append(f"{'='*60}\n\n")
                text_content.append(page_text)
                text_content.append(f"\n\n")
        
        doc.close()
        
        # Save to TXT file
        with open(output_txt_path, 'w', encoding='utf-8') as txt_file:
            txt_file.write(f"Pashto Grammar PDF - Text Extraction\n")
            txt_file.write(f"Extracted on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            txt_file.write(f"Source PDF: {pdf_path}\n")
            txt_file.write(f"Total Pages: {doc.page_count}\n")
            txt_file.write("="*80 + "\n\n")
            txt_file.writelines(text_content)
        
        print(f"✅ Text extracted successfully using PyMuPDF!")
        print(f"📝 Output saved to: {output_txt_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error with PyMuPDF: {e}")
        return False

def extract_pashto_grammar_pdf():
    """
    Main function to extract text from Pashto Grammar PDF
    """
    # File paths
    pdf_path = r"D:\Collect data\pashto Grammer.pdf"
    output_txt_path = r"D:\Collect data\pashto_grammar_extracted.txt"
    
    print("🚀 Starting Pashto Grammar PDF Text Extraction...")
    print("="*60)
    print(f"📁 PDF File: {pdf_path}")
    print(f"📝 Output TXT: {output_txt_path}")
    print("="*60)
    
    # Check if PDF file exists
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        print("Please check the file path and name.")
        return False
    
    print(f"✅ PDF file found: {os.path.getsize(pdf_path)} bytes")
    
    # Try PyMuPDF first (better for complex PDFs and Pashto text)
    print("\n🔄 Attempting extraction with PyMuPDF...")
    if extract_text_from_pdf_pymupdf(pdf_path, output_txt_path):
        return True
    
    # If PyMuPDF fails, try PyPDF2
    print("\n🔄 Attempting extraction with PyPDF2...")
    if extract_text_from_pdf_pypdf2(pdf_path, output_txt_path):
        return True
    
    print("❌ Both extraction methods failed.")
    return False

if __name__ == "__main__":
    success = extract_pashto_grammar_pdf()
    
    if success:
        print("\n🎉 PDF text extraction completed successfully!")
        print("📋 Summary:")
        print("  - Source: pashto Grammer.pdf")
        print("  - Output: pashto_grammar_extracted.txt")
        print("  - Location: D:\\Collect data\\")
        print("\n💡 You can now open the TXT file to view the extracted content.")
    else:
        print("\n❌ PDF extraction failed.")
        print("💡 Try installing required packages:")
        print("   pip install PyPDF2 PyMuPDF")
