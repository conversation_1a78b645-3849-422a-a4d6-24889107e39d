import os
from datetime import datetime

def extract_text_from_pdf_simple(pdf_path, output_txt_path):
    """
    Simple PDF text extraction using PyPDF2
    """
    try:
        import PyPDF2

        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            all_text = []

            print(f"📖 Total pages in PDF: {len(pdf_reader.pages)}")

            for page_num, page in enumerate(pdf_reader.pages, 1):
                print(f"📄 Extracting page {page_num}...")
                page_text = page.extract_text()
                if page_text.strip():
                    all_text.append(f"\n--- PAGE {page_num} ---\n")
                    all_text.append(page_text)
                    all_text.append("\n")

            # Save to TXT file
            with open(output_txt_path, 'w', encoding='utf-8') as txt_file:
                txt_file.write("PASHTO GRAMMAR - TEXT EXTRACTION\n")
                txt_file.write("="*50 + "\n")
                txt_file.write(f"Extracted on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                txt_file.write(f"Source: {os.path.basename(pdf_path)}\n")
                txt_file.write(f"Total Pages: {len(pdf_reader.pages)}\n")
                txt_file.write("="*50 + "\n\n")
                txt_file.writelines(all_text)

            print(f"✅ Text extracted successfully!")
            print(f"📝 Output saved to: {output_txt_path}")
            return True

    except ImportError:
        print("❌ PyPDF2 not installed. Trying alternative method...")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def extract_text_alternative(pdf_path, output_txt_path):
    """
    Alternative PDF text extraction method
    """
    try:
        import fitz  # PyMuPDF

        doc = fitz.open(pdf_path)
        all_text = []

        print(f"📖 Total pages in PDF: {doc.page_count}")

        for page_num in range(doc.page_count):
            page = doc[page_num]
            print(f"📄 Extracting page {page_num + 1}...")
            page_text = page.get_text()

            if page_text.strip():
                all_text.append(f"\n--- PAGE {page_num + 1} ---\n")
                all_text.append(page_text)
                all_text.append("\n")

        doc.close()

        # Save to TXT file
        with open(output_txt_path, 'w', encoding='utf-8') as txt_file:
            txt_file.write("PASHTO GRAMMAR - TEXT EXTRACTION\n")
            txt_file.write("="*50 + "\n")
            txt_file.write(f"Extracted on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            txt_file.write(f"Source: {os.path.basename(pdf_path)}\n")
            txt_file.write(f"Total Pages: {doc.page_count}\n")
            txt_file.write("="*50 + "\n\n")
            txt_file.writelines(all_text)

        print(f"✅ Text extracted successfully using PyMuPDF!")
        print(f"📝 Output saved to: {output_txt_path}")
        return True

    except ImportError:
        print("❌ PyMuPDF not installed.")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def extract_pashto_grammar_pdf():
    """
    Main function to extract text from Pashto Grammar PDF
    """
    # File paths
    pdf_path = r"D:\Collect data\pashto Grammer.pdf"
    output_txt_path = r"D:\Collect data\pashto_grammar_extracted.txt"

    print("🚀 Starting Pashto Grammar PDF Text Extraction...")
    print("="*60)
    print(f"📁 PDF File: {pdf_path}")
    print(f"📝 Output TXT: {output_txt_path}")
    print("="*60)

    # Check if PDF file exists
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        print("Please check the file path and name.")
        return False

    print(f"✅ PDF file found: {os.path.getsize(pdf_path)} bytes")

    # Try PyPDF2 first (simpler method)
    print("\n🔄 Attempting extraction with PyPDF2...")
    if extract_text_from_pdf_simple(pdf_path, output_txt_path):
        return True

    # If PyPDF2 fails, try PyMuPDF
    print("\n🔄 Attempting extraction with PyMuPDF...")
    if extract_text_alternative(pdf_path, output_txt_path):
        return True

    print("❌ Both extraction methods failed.")
    print("💡 Try installing required packages: pip install PyPDF2 PyMuPDF")
    return False

if __name__ == "__main__":
    success = extract_pashto_grammar_pdf()
    
    if success:
        print("\n🎉 PDF text extraction completed successfully!")
        print("📋 Summary:")
        print("  - Source: pashto Grammer.pdf")
        print("  - Output: pashto_grammar_extracted.txt")
        print("  - Location: D:\\Collect data\\")
        print("\n💡 You can now open the TXT file to view the extracted content.")
    else:
        print("\n❌ PDF extraction failed.")
        print("💡 Try installing required packages:")
        print("   pip install PyPDF2 PyMuPDF")
