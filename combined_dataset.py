import os
import json
import pandas as pd

# Path to your data folder
data_folder = "pashto_data"
output_file = "combined_dataset.txt"

# Function to read a text file
def read_txt(file_path):
    with open(file_path, "r", encoding="utf-8") as f:
        return f.read()

# Function to read a CSV file
def read_csv(file_path):
    df = pd.read_csv(file_path)
    text_data = []
    for col in df.columns:
        if df[col].dtype == "object":  # Only take text-like columns
            text_data.extend(df[col].dropna().tolist())
    return "\n".join(map(str, text_data))

# Function to read a JSON file
def read_json(file_path):
    with open(file_path, "r", encoding="utf-8") as f:
        data = json.load(f)
    text_data = []

    # Flatten JSON (recursive text extraction)
    def extract_text(obj):
        if isinstance(obj, dict):
            for v in obj.values():
                extract_text(v)
        elif isinstance(obj, list):
            for item in obj:
                extract_text(item)
        elif isinstance(obj, str):
            text_data.append(obj)

    extract_text(data)
    return "\n".join(text_data)

# Combine all files
combined_text = ""

for filename in os.listdir(data_folder):
    file_path = os.path.join(data_folder, filename)
    if filename.endswith(".txt"):
        combined_text += read_txt(file_path) + "\n"
    elif filename.endswith(".csv"):
        combined_text += read_csv(file_path) + "\n"
    elif filename.endswith(".json"):
        combined_text += read_json(file_path) + "\n"

# Save to one big TXT file
with open(output_file, "w", encoding="utf-8") as f:
    f.write(combined_text)

print(f"✅ Combined dataset saved to {output_file}")
