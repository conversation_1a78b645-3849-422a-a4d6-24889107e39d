"""
Extract Pashto text from all PDF files in the folder using OCR (pytesseract).
Run: pip install pytesseract pdf2image pillow
You must also install Tesseract and Pashto language data.
"""
import os
from pdf2image import convert_from_path
from PIL import Image
import pytesseract

def extract_pashto_text_with_ocr(pdf_filename, txt_filename):

    pashto_text = ""
    images = convert_from_path(pdf_filename)
    for img in images:
        text = pytesseract.image_to_string(img, lang='pus')
        # Filter only Pashto characters (Unicode range)
        pashto_only = ''.join([c for c in text if '\u0600' <= c <= '\u06FF' or c == '\u200c' or c == '\n' or c == ' '])
        pashto_text += pashto_only + "\n"
    with open(txt_filename, "w", encoding="utf-8") as f:
        f.write(pashto_text)
    print(f"OCR Pashto text saved to {txt_filename}")

if __name__ == "__main__":
    folder = os.path.dirname(__file__)
    for filename in os.listdir(folder):
        if filename.lower().endswith('.pdf'):
            pdf_path = os.path.join(folder, filename)
            txt_path = os.path.splitext(pdf_path)[0] + '_pashto_ocr.txt'
            print(f"Processing {filename} with OCR ...")
            extract_pashto_text_with_ocr(pdf_path, txt_path)
"""
Extract text from all PDF files in the folder and save to .txt files using pdfplumber.
Run: pip install pdfplumber
"""
import os

def extract_text_with_pdfplumber(pdf_filename, txt_filename):
    try:
        import pdfplumber
    except ImportError:
        print("pdfplumber not installed. Please run: pip install pdfplumber")
        return
    text = ""
    with pdfplumber.open(pdf_filename) as pdf:
        for page in pdf.pages:
            page_text = page.extract_text()
            if page_text:
                text += page_text + "\n"
    with open(txt_filename, "w", encoding="utf-8") as f:
        f.write(text)
    print(f"Extracted text saved to {txt_filename}")

if __name__ == "__main__":
    folder = os.path.dirname(__file__)
    for filename in os.listdir(folder):
        if filename.lower().endswith('.pdf'):
            pdf_path = os.path.join(folder, filename)
            txt_path = os.path.splitext(pdf_path)[0] + '.txt'
            print(f"Processing {filename} ...")
            extract_text_with_pdfplumber(pdf_path, txt_path)
import pytesseract
from PIL import Image

# Load scanned page image
img = Image.open("pashto_page.png")

# Extract Pashto text
text = pytesseract.image_to_string(img, lang="pus")  # 'pus' = Pashto language

with open("pashto_books_data.txt", "w", encoding="utf-8") as f:
    f.write(text)
